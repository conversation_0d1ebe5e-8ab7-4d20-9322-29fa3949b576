const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const getChildren = async (req, res) => {
  try {
    const userId = req.user.id;

    const parent = await prisma.parent.findUnique({
      where: { user_id: userId },
      include: { 
        students: {
          include: {
            user: true
          }
        } 
      }
    });

    if (!parent) {
      return res.status(404).json({ error: 'Parent not found' });
    }

    const children = parent.students.map(student => ({
      id: student.id,
      user_id: student.user_id,
      name: student.user.name,
      email: student.user.email,
      grade_level: student.grade_level,
      avatar: student.user.name.split(' ').map(n => n[0]).join('')
    }));

    res.status(200).json(children);
  } catch (error) {
    console.error('getChildren error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch children', 
      details: error.message 
    });
  } finally {
    await prisma.$disconnect();
  }
};
const getTeachers = async (req, res) => {
  try {
    console.log('getTeachers: Fetching teachers');
    const teachers = await prisma.teacher.findMany({
      include: { user: true },
    });
    console.log('getTeachers: teachers:', JSON.stringify(teachers, null, 2));

    const teacherData = teachers.length
      ? teachers.map(teacher => ({
          id: teacher.id,
          name: teacher.user.name || 'Unknown',
          subjects: teacher.subjects || [],
        }))
      : [];
    console.log('getTeachers: teacherData:', JSON.stringify(teacherData, null, 2));

    res.status(200).json(teacherData);
  } catch (error) {
    console.error('getTeachers error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to fetch teachers', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const scheduleSession = async (req, res) => {
  try {
    const { childId, teacherId, subject, date, time } = req.body;
    const userId = req.user.id;
    console.log('scheduleSession: Request body:', JSON.stringify(req.body, null, 2), 'userId:', userId);

    if (!childId || !teacherId || !subject || !date || !time) {
      console.log('scheduleSession: Missing required fields');
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const parent = await prisma.parent.findUnique({ where: { user_id: userId } });
    console.log('scheduleSession: parent:', JSON.stringify(parent, null, 2));
    if (!parent) {
      console.log('scheduleSession: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const student = await prisma.student.findUnique({ where: { id: parseInt(childId) } });
    console.log('scheduleSession: student:', JSON.stringify(student, null, 2));
    if (!student || !student.parents.some(p => p.id === parent.id)) {
      console.log('scheduleSession: Invalid or unlinked childId:', childId);
      return res.status(400).json({ error: 'Invalid or unlinked child' });
    }

    const teacher = await prisma.teacher.findUnique({ where: { id: parseInt(teacherId) } });
    console.log('scheduleSession: teacher:', JSON.stringify(teacher, null, 2));
    if (!teacher) {
      console.log('scheduleSession: Teacher not found for teacherId:', teacherId);
      return res.status(404).json({ error: 'Teacher not found' });
    }

    const scheduledTime = new Date(`${date}T${time}:00.000Z`);
    if (isNaN(scheduledTime.getTime())) {
      console.log('scheduleSession: Invalid date or time:', date, time);
      return res.status(400).json({ error: 'Invalid date or time' });
    }

    const lesson = await prisma.lesson.create({
      data: {
        teacher_id: parseInt(teacherId),
        student_id: parseInt(childId),
        subject,
        scheduled_time: scheduledTime,
        status: 'pending',
      },
    });
    console.log('scheduleSession: Created lesson:', JSON.stringify(lesson, null, 2));

    const notification = await prisma.notification.create({
      data: {
        recipient_id: teacher.user_id,
        type: 'lesson_request',
        message: `New lesson request for ${subject} on ${date} at ${time}`,
        status: 'sent',
      },
    });
    console.log('scheduleSession: Created notification:', JSON.stringify(notification, null, 2));

    res.status(200).json({ message: 'Session scheduled successfully', lesson });
  } catch (error) {
    console.error('scheduleSession error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to schedule session', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

// In your parentController.js

const linkChild = async (req, res) => {
  try {
    const { student_id } = req.body;
    const userId = req.user.id;

    if (!student_id) {
      return res.status(400).json({ error: 'Missing student_id' });
    }

    // Find parent
    const parent = await prisma.parent.findUnique({
      where: { user_id: userId }
    });

    if (!parent) {
      return res.status(404).json({ error: 'Parent not found' });
    }

    // Find student
    const student = await prisma.student.findUnique({
      where: { id: parseInt(student_id) },
      include: { user: true }
    });

    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Check if already linked
    const isLinked = await prisma.parent.findFirst({
      where: {
        id: parent.id,
        students: {
          some: { id: parseInt(student_id) }
        }
      }
    });

    if (isLinked) {
      return res.status(409).json({ error: 'Student already linked to this parent' });
    }

    // Link student to parent
    await prisma.parent.update({
      where: { id: parent.id },
      data: {
        students: {
          connect: { id: parseInt(student_id) }
        }
      }
    });

    // Return success with student data
    res.status(200).json({
      id: student.id,
      name: student.user.name,
      grade_level: student.grade_level
    });

  } catch (error) {
    console.error('linkChild error:', error);
    res.status(500).json({ 
      error: 'Failed to link child', 
      details: error.message 
    });
  }
};

const getReports = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('getReports: userId:', userId);

    const parent = await prisma.parent.findUnique({
      where: { user_id: userId },
      include: {
        students: {
          include: {
            lessons: {
              include: {
                teacher: { include: { user: true } },
                student: { include: { user: true } },
              },
            },
          },
        },
      },
    });
    console.log('getReports: parent:', JSON.stringify(parent, null, 2));

    if (!parent) {
      console.log('getReports: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const reports = parent.students.length
      ? parent.students.flatMap(student =>
          student.lessons
            .filter(lesson => lesson.feedback)
            .map(lesson => ({
              child: student.user.name || 'Unknown',
              subject: lesson.subject || 'N/A',
              teacher: lesson.teacher?.user.name || 'Unknown',
              date: lesson.scheduled_time.toISOString(),
              grade: lesson.grade || 'N/A',
              feedback: lesson.feedback || '',
            }))
        )
      : [];
    console.log('getReports: reports:', JSON.stringify(reports, null, 2));

    res.status(200).json(reports);
  } catch (error) {
    console.error('getReports error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to fetch reports', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const signPlan = async (req, res) => {
  try {
    const { subscription_id, signature } = req.body;
    const userId = req.user.id;
    console.log('signPlan: Request body:', JSON.stringify(req.body, null, 2), 'userId:', userId);

    if (!subscription_id || !signature) {
      console.log('signPlan: Missing subscription_id or signature');
      return res.status(400).json({ error: 'Missing subscription_id or signature' });
    }

    const parent = await prisma.parent.findUnique({ where: { user_id: userId } });
    console.log('signPlan: parent:', JSON.stringify(parent, null, 2));
    if (!parent) {
      console.log('signPlan: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const subscription = await prisma.subscription.findUnique({
      where: { id: parseInt(subscription_id) },
      include: { student: { include: { parents: true } } },
    });
    console.log('signPlan: subscription:', JSON.stringify(subscription, null, 2));
    if (!subscription) {
      console.log('signPlan: Subscription not found for subscription_id:', subscription_id);
      return res.status(404).json({ error: 'Subscription not found' });
    }

    if (!subscription.student.parents.some(p => p.id === parent.id)) {
      console.log('signPlan: Parent not linked to subscription’s student, subscription_id:', subscription_id);
      return res.status(403).json({ error: 'Parent not authorized for this subscription' });
    }

    const eSignature = await prisma.eSignature.create({
      data: {
        parent_id: parent.id,
        subscription_id: parseInt(subscription_id),
        signature,
      },
    });
    console.log('signPlan: Created eSignature:', JSON.stringify(eSignature, null, 2));

    const updatedSubscription = await prisma.subscription.update({
      where: { id: parseInt(subscription_id) },
      data: { status: 'approved' },
    });
    console.log('signPlan: Updated subscription:', JSON.stringify(updatedSubscription, null, 2));

    res.status(200).json({ message: 'Plan signed successfully', eSignature });
  } catch (error) {
    console.error('signPlan error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to sign plan', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const getDashboardData = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('getDashboardData: userId:', userId);

    const parent = await prisma.parent.findUnique({
      where: { user_id: userId },
      include: {
        students: {
          include: { 
            user: true, 
            lessons: {
              include: {
                teacher: {
                  include: {
                    user: true
                  }
                }
              }
            }
          }
        },
      },
    });

    if (!parent) {
      return res.status(404).json({ error: 'Parent not found' });
    }

    // Format children data for dashboard
    const children = parent.students.map(student => ({
      name: student.user.name,
      grade: student.grade_level,
      totalSessions: student.lessons.length,
      completedSessions: student.lessons.filter(l => l.status === 'completed').length,
      averageGrade: calculateAverageGrade(student.lessons),
      subjects: getUniqueSubjects(student.lessons)
    }));

    // Format pending approvals (using lessons with pending status)
    const pendingApprovals = parent.students.flatMap(student => 
      student.lessons
        .filter(lesson => lesson.status === 'pending_approval')
        .map(lesson => ({
          child: student.user.name,
          teacher: lesson.teacher?.user?.name || 'Unknown Teacher',
          subject: lesson.subject || 'General',
          date: formatLessonDate(lesson.scheduled_time),
          duration: '1 hour', // Default duration
          cost: calculateLessonCost(lesson) // You'll need to implement this
        }))
    );

    // Format recent reports (completed lessons with feedback)
    const recentReports = parent.students.flatMap(student => 
      student.lessons
        .filter(lesson => lesson.status === 'completed' && lesson.feedback)
        .slice(0, 2) // Limit to 2 most recent
        .map(lesson => ({
          child: student.user.name,
          subject: lesson.subject || 'General',
          teacher: lesson.teacher?.user?.name || 'Unknown Teacher',
          date: formatLessonDate(lesson.scheduled_time),
          grade: lesson.grade || 'A-', // Default grade
          feedback: lesson.feedback
        }))
    );

    // Format weekly schedule
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    const weeklySchedule = parent.students.flatMap(student => 
      student.lessons
        .filter(lesson => lesson.scheduled_time >= now && lesson.scheduled_time <= nextWeek)
        .map(lesson => ({
          child: student.user.name.split(' ')[0], // First name only
          subject: lesson.subject || 'General',
          time: formatLessonTime(lesson.scheduled_time),
          status: lesson.status === 'confirmed' ? 'confirmed' : 
                 lesson.status === 'pending' ? 'pending' : 'upcoming'
        }))
    );

    // Calculate family stats
    const stats = {
      activeChildren: parent.students.length,
      totalSessions: parent.students.reduce((sum, student) => sum + student.lessons.length, 0),
      avgPerformance: Math.round(children.reduce((sum, child) => sum + child.averageGrade, 0) / children.length || 0)
    };

    res.status(200).json({ 
      children, 
      pendingApprovals, 
      recentReports, 
      weeklySchedule,
      stats
    });

  } catch (error) {
    console.error('getDashboardData error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard data', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

// Helper functions
function calculateAverageGrade(lessons) {
  const gradedLessons = lessons.filter(l => l.grade && !isNaN(parseFloat(l.grade)));
  if (gradedLessons.length === 0) return 85; // Default average
  return Math.round(gradedLessons.reduce((sum, l) => sum + parseFloat(l.grade), 0) / gradedLessons.length);
}

function getUniqueSubjects(lessons) {
  const subjects = new Set();
  lessons.forEach(lesson => {
    if (lesson.subject) subjects.add(lesson.subject);
  });
  return Array.from(subjects);
}

function formatLessonDate(date) {
  return new Date(date).toLocaleString('en-US', {
    weekday: 'long',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

function formatLessonTime(date) {
  return new Date(date).toLocaleString('en-US', {
    weekday: 'long',
    hour: '2-digit',
    minute: '2-digit'
  });
}

function calculateLessonCost(lesson) {
  // Implement your cost calculation logic here
  return '$25'; // Default cost
}

const getSettingsData = async (req, res) => {
  try {
    const userId = req.user.id;
    const parent = await prisma.parent.findUnique({
      where: { user_id: userId },
      include: {
        user: true,
        students: {
          include: {
            user: true
          }
        }
      }
    });

    if (!parent) {
      return res.status(404).json({ error: 'Parent not found' });
    }

    const response = {
      profile: {
        name: parent.user.name,
        email: parent.user.email,
        phone: parent.user.phone,
        relationship: 'Parent',
        language: parent.user.language || 'en'
      },
      children: parent.students.map(student => ({
        id: student.id,
        name: student.user.name,
        grade: student.grade_level,
        avatar: student.user.name.split(' ').map(n => n[0]).join('')
      })),
      notifications: {
        childProgress: true,
        sessionApprovals: true,
        teacherMessages: true,
        paymentUpdates: true,
        systemUpdates: false
      },
      privacySettings: {
        shareProgressWithTeachers: true,
        receiveRecommendations: true,
        allowDirectContact: false
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('getSettingsData error:', error);
    res.status(500).json({ error: 'Failed to fetch settings data', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const updateSettings = async (req, res) => {
  try {
    const userId = req.user.id;
    const { profile, notifications, privacySettings } = req.body;

    // Update user profile
    await prisma.user.update({
      where: { id: userId },
      data: {
        name: profile.name,
        email: profile.email,
        phone: profile.phone,
        language: profile.language
      }
    });

    // In a real app, you'd save notification and privacy preferences to the database
    // For now, we'll just return the updated data

    res.status(200).json({ 
      message: 'Settings updated successfully',
      profile,
      notifications,
      privacySettings
    });
  } catch (error) {
    console.error('updateSettings error:', error);
    res.status(500).json({ error: 'Failed to update settings', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

// Keep your existing functions (getChildren, getTeachers, etc.) but ensure they return
// data in the format expected by the frontend

module.exports = {
  getDashboardData,
  getSettingsData,
  updateSettings,
  getChildren,
  getTeachers,
  scheduleSession,
  linkChild,
  getReports,
  signPlan
};
