const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const path = require("path");
const fs = require("fs-extra");

const prisma = new PrismaClient();

// Registers a new user in the EducationPlatform database, creating a User record and, if applicable, a linked Student, Teacher, or Parent record.
// - Input: JSON body with name, email, phone, password, role, language, and (for students) grade_level.
// - Output: JSON response with a success message and the created user's ID, or an error message if registration fails.
const register = async (req, res) => {
  const { name, email, phone, password, role, language, grade_level, grade_levels, subjects, description, cin_number, number_of_children, children_grades, attachments } = req.body;

  try {
    // Check for existing email
    const existingEmail = await prisma.user.findUnique({
      where: { email },
    });
    if (existingEmail) {
      return res.status(400).json({
        error: "Email already exists",
        field: "email",
      });
    }

    // Check for existing phone
    if (phone) {
      const existingPhone = await prisma.user.findUnique({
        where: { phone },
      });
      if (existingPhone) {
        return res.status(400).json({
          error: "Phone number already exists",
          field: "phone",
        });
      }
    }

    // Check for existing CIN for parents
    if (role === "parent" && cin_number) {
      const existingCIN = await prisma.parent.findUnique({
        where: { cin_number },
      });
      if (existingCIN) {
        return res.status(400).json({
          error: "CIN number already exists",
          field: "cin_number",
        });
      }
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    // Prepare role-specific data
    let roleData = {};

    if (role === "student") {
      roleData = { student: { create: { grade_level } } };
    } else if (role === "teacher") {
      roleData = {
        teacher: {
          create: {
            subjects: subjects || [],
            grade_levels: grade_levels || [],
            description: description || "",
            rates: 0,
            availability: {},
            verified_status: "pending",
            certificates: [],
          },
        },
      };
    } else if (role === "parent") {
      roleData = {
        parent: {
          create: {
            cin_number: cin_number || null,
          },
        },
      };
    }

    // Create user with role-specific data
    const user = await prisma.user.create({
      data: {
        name,
        email,
        phone,
        password_hash: hashedPassword,
        role,
        language,
        pic_url: "/uploads/profile-pics/default-profile.png",
        status: role === "teacher" ? "pending" : "active",
        ...roleData,
      },
      include: {
        student: role === "student",
        teacher: role === "teacher",
        parent: role === "parent",
      },
    });

    // Generate OTP for email confirmation
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    await prisma.otp.create({
      data: {
        user_id: user.id,
        code: otpCode,
        expires_at: new Date(Date.now() + 10 * 60 * 1000), // Expires in 10 minutes
      },
    });

    // Prepare response data
    const responseData = {
      message: "User registered, OTP sent",
      userId: user.id,
      otpCode,
    };

    // Add studentId to response if user is a student
    if (role === "student" && user.student) {
      responseData.studentId = user.student.id;
    }

    res.status(201).json(responseData);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Confirms a user's registration by verifying their OTP code.
// - Input: JSON body with user_id and code.
// - Output: JSON response with a success message, or an error message if OTP is invalid or expired.
const confirmOtp = async (req, res) => {
  const { user_id, code } = req.body;
  try {
    const otp = await prisma.otp.findFirst({
      where: { user_id, code, expires_at: { gt: new Date() } },
    });
    if (!otp) return res.status(400).json({ error: "Invalid or expired OTP" });
    await prisma.user.update({
      where: { id: user_id },
      data: { status: "active" },
    });
    await prisma.otp.delete({ where: { id: otp.id } });
    res.json({ message: "OTP confirmed, user activated" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Authenticates a user by verifying their email and password, returning a JWT for session management.
// - Input: JSON body with email and password.
// - Output: JSON response with a JWT containing user ID, role, and email, or an error message if authentication fails.
const login = async (req, res) => {
  const { email, password } = req.body;
  console.log("login: Attempting login for email:", email);
  try {
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      console.log("login: User not found for email:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }
    const isValid = await bcrypt.compare(password, user.password_hash);
    if (!isValid) {
      console.log("login: Invalid password for email:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }
    const token = jwt.sign({ id: user.id, role: user.role }, process.env.JWT_SECRET, { expiresIn: "1h" });
    console.log("login: Token generated for user:", { id: user.id, role: user.role });
    res.status(200).json({ token });
  } catch (error) {
    console.error("login: Error:", error);
    res.status(500).json({ error: "Login failed" });
  } finally {
    await prisma.$disconnect();
  }
};

// Retrieves the profile of the authenticated user, including associated Student, Teacher, or Parent data.
// - Input: JWT in the Authorization header (verified by authMiddleware).
// - Output: JSON response with the user's details, including linked student, teacher, or parent records, or an error message if the user is not found.
const getProfile = async (req, res) => {
  try {
    console.log("getProfile: Fetching profile for user ID:", req.user.id);
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { student: true, teacher: true, parent: true },
    });

    if (!user) {
      console.log("getProfile: User not found for ID:", req.user.id);
      return res.status(404).json({ error: "User not found" });
    }

    console.log("getProfile: User found, sending response");
    res.json(user);
  } catch (error) {
    console.error("getProfile: Error:", error);
    res.status(400).json({ error: error.message });
  }
};

// Updates the authenticated user's profile information in the User table.
// - Input: JSON body with name, email, phone, and language; JWT in the Authorization header.
// - Output: JSON response with the updated user data, or an error message if the update fails.

const updateProfile = async (req, res) => {
  const { name, email, phone } = req.body;
  try {
    const updateData = { name, email, phone };

    if (req.file) {
      console.log("Uploaded file:", req.file); // Debug log

      // Get current user data
      const user = await prisma.user.findUnique({ where: { id: req.user.id } });

      // Delete old profile pic if exists
      if (user.pic_url) {
        const oldFilePath = path.join(__dirname, "../../public", user.pic_url);
        try {
          await fs.remove(oldFilePath);
          console.log("Deleted old profile picture:", oldFilePath);
        } catch (err) {
          console.error("Error deleting old profile picture:", err);
        }
      }

      // Save relative path to DB
      updateData.pic_url = `/uploads/profile-pics/${req.file.filename}`;
      console.log("New profile picture path:", updateData.pic_url);
    }

    const updatedUser = await prisma.user.update({
      where: { id: req.user.id },
      data: updateData,
    });

    console.log("Updated user:", updatedUser); // Debug log

    res.json({
      ...updatedUser,
      pic_url: updatedUser.pic_url || "/default-profile.png",
    });
  } catch (error) {
    console.error("Profile update error:", error);
    res.status(400).json({
      error: "Failed to update profile",
      details: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};
const getProfilePicUrl = (user) => {
  return user.pic_url || "/default-profile.png";
};
const changePassword = async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  try {
    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: "Current and new password are required" });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ error: "Password must be at least 6 characters" });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({ error: "Current password is incorrect" });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password in database
    await prisma.user.update({
      where: { id: userId },
      data: { password_hash: hashedPassword },
    });

    // Invalidate all existing sessions/tokens if needed
    // (Implementation depends on your session management)

    res.status(200).json({ message: "Password changed successfully" });
  } catch (error) {
    console.error("Error changing password:", error);
    res.status(500).json({
      error: "Failed to change password",
      details: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  } finally {
    await prisma.$disconnect();
  }
};
const createStudent = async (req, res) => {
  try {
    const { user_id, grade_level } = req.body;

    // Validate input
    if (!user_id || !grade_level) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Check if student already exists
    const existingStudent = await prisma.student.findUnique({
      where: { user_id: parseInt(user_id) },
    });

    if (existingStudent) {
      return res.status(409).json({ error: "Student already exists for this user" });
    }

    // Create the student
    const student = await prisma.student.create({
      data: {
        user_id: parseInt(user_id),
        grade_level,
      },
      include: {
        user: true,
      },
    });

    res.status(201).json({
      id: student.id,
      user_id: student.user_id,
      name: student.user.name,
      email: student.user.email,
      grade_level: student.grade_level,
    });
  } catch (error) {
    console.error("createStudent error:", error);
    res.status(500).json({
      error: "Failed to create student",
      details: error.message,
    });
  }
};
// Soft-deletes a user by setting their status to 'deleted' in the User table.
// - Input: User ID in the URL parameter; JWT in the Authorization header (must be authorized to delete).
// - Output: JSON response with a success message, or an error message if the deletion fails.
const deleteUser = async (req, res) => {
  try {
    await prisma.user.update({
      where: { id: parseInt(req.params.id) },
      data: { status: "deleted" },
    });
    res.json({ message: "User deleted" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Requests a GDPR data export or deletion for the authenticated user.
// - Input: JSON body with type ("export" or "delete"); JWT in the Authorization header.
// - Output: JSON response with a success message and request ID, or an error message if the request fails.
const requestData = async (req, res) => {
  const { type } = req.body;
  try {
    if (!["export", "delete"].includes(type)) {
      return res.status(400).json({ error: "Invalid request type" });
    }
    const dataRequest = await prisma.dataRequest.create({
      data: {
        user_id: req.user.id,
        type,
        status: "pending",
      },
    });
    res.status(201).json({ message: "Data request created", requestId: dataRequest.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { register, confirmOtp, login, getProfile, changePassword, updateProfile, deleteUser, getProfilePicUrl, requestData, createStudent };
