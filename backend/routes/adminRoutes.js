const express = require("express");
const router = express.Router();
const adminController = require("../controllers/adminController");
const auth = require("../middleware/auth");

// Get admin dashboard overview
router.get("/dashboard", auth(), adminController.getDashboard);

// Manage user roles (assign/remove admin/moderator)
router.put("/roles", auth(), adminController.manageRoles);

// Issue a refund for a payment
router.post("/refund", auth(), adminController.issueRefund);

// Update system policies or terms
router.put("/policies", auth(), adminController.updatePolicies);

// Process GDPR data export or deletion requests
router.post("/data-request", auth(), adminController.processDataRequest);

// Get pending teachers for review
router.get("/pending-teachers", auth(), adminController.getPendingTeachers);

// Get approved teachers
router.get("/approved-teachers", auth(), adminController.getApprovedTeachers);

// Get rejected teachers
router.get("/rejected-teachers", auth(), adminController.getRejectedTeachers);

// Approve or reject teacher applications
router.put("/teacher-status", auth(), adminController.manageTeacherStatus);

module.exports = router;
