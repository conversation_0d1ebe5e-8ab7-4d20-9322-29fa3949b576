const express = require('express');
const router = express.Router();
const parentController = require('../controllers/parentController');
const auth = require('../middleware/auth');

router.get('/dashboard', auth(['parent']), parentController.getDashboardData);
router.get('/settings', auth(['parent']), parentController.getSettingsData);
router.put('/settings', auth(['parent']), parentController.updateSettings);
router.get('/children', auth(['parent']), parentController.getChildren);
router.get('/teachers', auth(['parent']), parentController.getTeachers);
router.post('/schedule', auth(['parent']), parentController.scheduleSession);
router.post('/link-child', auth(['parent']), parentController.linkChild);
router.get('/reports', auth(['parent']), parentController.getReports);
router.post('/sign-plan', auth(['parent']), parentController.signPlan);

module.exports = router;