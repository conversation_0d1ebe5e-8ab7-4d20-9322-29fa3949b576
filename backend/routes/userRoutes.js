const express = require("express");
const router = express.Router();
const userController = require("../controllers/userController");
const auth = require("../middleware/auth");
const upload = require('../middleware/upload');

// Register a new user
router.post("/register", userController.register);

// Confirm OTP for user registration
router.post("/confirm-otp", userController.confirmOtp);

// Login user and return JWT
router.post("/login", userController.login);

// Test endpoint without auth
router.get("/test", (req, res) => {
  console.log("Test endpoint hit");
  res.json({ message: "Test endpoint working" });
});

// Test endpoint with auth
router.get("/test-auth", auth(), (req, res) => {
  console.log("Test auth endpoint hit, user:", req.user);
  res.json({ message: "Auth test working", user: req.user });
});

// Get authenticated user's profile
router.get("/profile", auth(), userController.getProfile);


// Delete a user (soft delete)
router.delete("/:id", auth(), userController.deleteUser);

// Request GDPR data export or deletion
router.post("/data-request", auth(), userController.requestData);
router.post('/students', auth(['admin', 'parent']), userController.createStudent);
router.put('/change-password', auth(), userController.changePassword);
router.put('/profile', 
  auth(),
  (req, res, next) => {
    console.log('Before upload middleware - req.files:', req.files);
    console.log('Before upload middleware - req.file:', req.file);
    next();
  },
  upload.single('profilePic'),
  (req, res, next) => {
    console.log('After upload middleware - req.files:', req.files);
    console.log('After upload middleware - req.file:', req.file);
    console.log('Request body:', req.body);
    next();
  },
  userController.updateProfile
);
// Add this to userRoutes.js
module.exports = router;
