const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");

const prisma = new PrismaClient();

async function main() {
  const hashedPassword = await bcrypt.hash("password123", 10);
  const teacherUser = await prisma.user.create({
    data: {
      name: "Dr. <PERSON>",
      email: "<EMAIL>",
      phone: "1234567890",
      password_hash: hashedPassword,
      role: "teacher",
      language: "ar",
      status: "active",
    },
  });

  const teacher = await prisma.teacher.create({
    data: {
      user_id: teacherUser.id,
      subjects: ["Math", "Physics"],
      rates: 50.0,
      availability: { Monday: ["10:00-12:00", "14:00-16:00"] },
      verified_status: "approved",
      rating: 4.9,
      photo_path: "/uploads/teacher-photo.png", // Added sample photo path
    },
  });

  await prisma.teacherStats.create({
    data: {
      teacher_id: teacher.id,
      monthly_earnings: 2450000,
      active_students: 24,
      teaching_hours: 156,
      total_earnings: 18500000,
    },
  });

  const studentUser = await prisma.user.create({
    data: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "0987654321",
      password_hash: hashedPassword,
      role: "student",
      language: "ar",
      status: "active",
    },
  });

  const student = await prisma.student.create({
    data: {
      user_id: studentUser.id,
      grade_level: "10",
    },
  });

  await prisma.lesson.createMany({
    data: [
      {
        teacher_id: teacher.id,
        student_id: student.id,
        scheduled_time: new Date("2025-07-03T10:00:00Z"),
        status: "confirmed",
        subject: "Math",
      },
      {
        teacher_id: teacher.id,
        student_id: student.id,
        scheduled_time: new Date("2025-07-03T14:00:00Z"),
        status: "pending",
        subject: "Physics",
      },
    ],
  });

  console.log("Database seeded with sample teacher data");
}

main()
  .catch((e) => console.error(e))
  .finally(async () => await prisma.$disconnect());
