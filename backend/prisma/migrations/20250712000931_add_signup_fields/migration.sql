/*
  Warnings:

  - You are about to drop the column `subject` on the `Lesson` table. All the data in the column will be lost.
  - You are about to drop the column `parent_id` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the column `teacher_id` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the `Assignment` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_ParentToStudent` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[cin_number]` on the table `Parent` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[phone]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "Assignment" DROP CONSTRAINT "Assignment_lesson_id_fkey";

-- DropForeignKey
ALTER TABLE "Assignment" DROP CONSTRAINT "Assignment_student_id_fkey";

-- DropForeignKey
ALTER TABLE "Notification" DROP CONSTRAINT "Notification_parent_id_fkey";

-- DropForeignKey
ALTER TABLE "Notification" DROP CONSTRAINT "Notification_teacher_id_fkey";

-- DropForeignKey
ALTER TABLE "Subscription" DROP CONSTRAINT "Subscription_student_id_fkey";

-- DropForeignKey
ALTER TABLE "TeacherStats" DROP CONSTRAINT "TeacherStats_teacher_id_fkey";

-- DropForeignKey
ALTER TABLE "_ParentToStudent" DROP CONSTRAINT "_ParentToStudent_A_fkey";

-- DropForeignKey
ALTER TABLE "_ParentToStudent" DROP CONSTRAINT "_ParentToStudent_B_fkey";

-- AlterTable
ALTER TABLE "ApprovalRequest" ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "DataRequest" ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Flag" ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Lesson" DROP COLUMN "subject",
ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Notification" DROP COLUMN "parent_id",
DROP COLUMN "teacher_id",
ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Parent" ADD COLUMN     "cin_number" TEXT;

-- AlterTable
ALTER TABLE "Payment" ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Report" ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Student" ALTER COLUMN "grade_level" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Subscription" ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Teacher" ADD COLUMN     "description" TEXT,
ADD COLUMN     "grade_levels" TEXT[] DEFAULT ARRAY[]::TEXT[];

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "pic_url" TEXT DEFAULT '/uploads/profile-pics/default-profile.png',
ALTER COLUMN "language" DROP NOT NULL,
ALTER COLUMN "status" DROP DEFAULT;

-- DropTable
DROP TABLE "Assignment";

-- DropTable
DROP TABLE "_ParentToStudent";

-- CreateTable
CREATE TABLE "_ParentStudent" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_ParentStudent_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_ParentStudent_B_index" ON "_ParentStudent"("B");

-- CreateIndex
CREATE UNIQUE INDEX "Parent_cin_number_key" ON "Parent"("cin_number");

-- CreateIndex
CREATE UNIQUE INDEX "User_phone_key" ON "User"("phone");

-- AddForeignKey
ALTER TABLE "TeacherStats" ADD CONSTRAINT "TeacherStats_teacher_id_fkey" FOREIGN KEY ("teacher_id") REFERENCES "Teacher"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ParentStudent" ADD CONSTRAINT "_ParentStudent_A_fkey" FOREIGN KEY ("A") REFERENCES "Parent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ParentStudent" ADD CONSTRAINT "_ParentStudent_B_fkey" FOREIGN KEY ("B") REFERENCES "Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;
