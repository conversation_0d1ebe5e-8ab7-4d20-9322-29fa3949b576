import { z } from "zod";

// Available grades and subjects
export const GRADES = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"] as const;

export const SUBJECTS = ["Mathematics", "Science", "English", "Arabic", "Geography", "Art", "Programming", "Music", "Physics", "Chemistry", "Biology", "History", "French", "Spanish"] as const;

// Base schema for common fields (without refinement)
const baseSignupFields = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(2, t("signup.validation.nameRequired")),
    email: z.string().email(t("signup.validation.emailRequired")),
    phone: z.string().min(10, t("signup.validation.phoneRequired")),
    password: z.string().min(6, t("signup.validation.passwordRequired")),
    confirmPassword: z.string().min(6, t("signup.validation.confirmPasswordRequired")),
    language: z.string().min(1, t("signup.validation.languageRequired")),
  });

// Password confirmation refinement function
const addPasswordConfirmationRefinement = (schema: any, t: (key: string) => string) =>
  schema.refine((data: any) => data.password === data.confirmPassword, {
    message: t("signup.validation.passwordMismatch"),
    path: ["confirmPassword"],
  });

// Student signup schema
export const createStudentSignupSchema = (t: (key: string) => string) =>
  addPasswordConfirmationRefinement(
    baseSignupFields(t).extend({
      role: z.literal("student"),
      grade_level: z.enum(GRADES, {
        required_error: t("signup.validation.gradeLevelRequired"),
      }),
    }),
    t
  );

// Teacher signup schema
export const createTeacherSignupSchema = (t: (key: string) => string) =>
  addPasswordConfirmationRefinement(
    baseSignupFields(t).extend({
      role: z.literal("teacher"),
      grade_levels: z.array(z.enum(GRADES)).min(1, t("signup.validation.gradeLevelsRequired")),
      subjects: z.array(z.enum(SUBJECTS)).min(1, t("signup.validation.subjectsRequired")),
      description: z.string().min(50, t("signup.validation.descriptionRequired")),
      attachments: z.array(z.instanceof(File)).optional(),
    }),
    t
  );

// Parent signup schema
export const createParentSignupSchema = (t: (key: string) => string) =>
  addPasswordConfirmationRefinement(
    baseSignupFields(t).extend({
      role: z.literal("parent"),
      cin_number: z.string().min(8, t("signup.validation.cinRequired")),
      number_of_children: z.number().min(1, t("signup.validation.numberOfChildrenRequired")),
      children_grades: z.array(z.enum(GRADES)).min(1, t("signup.validation.childrenGradesRequired")),
    }),
    t
  );

// Union type for all signup schemas
export const createSignupSchema = (t: (key: string) => string, role?: string) => {
  switch (role) {
    case "student":
      return createStudentSignupSchema(t);
    case "teacher":
      return createTeacherSignupSchema(t);
    case "parent":
      return createParentSignupSchema(t);
    default:
      // Default schema for role selection
      return z.object({
        role: z.enum(["student", "teacher", "parent"], {
          required_error: t("signup.validation.roleRequired"),
        }),
      });
  }
};

// Type definitions
export type StudentSignupData = z.infer<ReturnType<typeof createStudentSignupSchema>>;
export type TeacherSignupData = z.infer<ReturnType<typeof createTeacherSignupSchema>>;
export type ParentSignupData = z.infer<ReturnType<typeof createParentSignupSchema>>;

export type SignupData = StudentSignupData | TeacherSignupData | ParentSignupData;
