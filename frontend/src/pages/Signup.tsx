import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Link } from "react-router-dom";
import { CheckCircle, ArrowLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import StudentSignupForm from "@/components/signup/StudentSignupForm";
import TeacherSignupForm from "@/components/signup/TeacherSignupForm";
import ParentSignupForm from "@/components/signup/ParentSignupForm";
import { createSignupSchema, type SignupData, type StudentSignupData, type TeacherSignupData, type ParentSignupData } from "@/schemas/signupSchemas";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

// Role selection schema
const createRoleSelectionSchema = (t: (key: string) => string) =>
  z.object({
    role: z.enum(["student", "teacher", "parent"], {
      required_error: t("signup.validation.roleRequired"),
    }),
  });

type RoleSelectionData = z.infer<ReturnType<typeof createRoleSelectionSchema>>;

const Signup: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [otpCode, setOtpCode] = useState("");
  const [userId, setUserId] = useState<number | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isConfirmingOtp, setIsConfirmingOtp] = useState(false);
  const { register, confirmOtp } = useAuth();
  const { isRTL, t } = useLanguage();

  const roleSelectionSchema = createRoleSelectionSchema(t);

  const roleForm = useForm<RoleSelectionData>({
    resolver: zodResolver(roleSelectionSchema),
    defaultValues: {
      role: undefined,
    },
  });

  const handleRoleSelection = (data: RoleSelectionData) => {
    setSelectedRole(data.role);
  };

  const handleSignupSubmit = async (data: SignupData) => {
    setIsLoading(true);
    try {
      // Prepare the registration data based on role
      let registrationData: any = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        password: data.password,
        role: data.role,
        language: data.language,
      };

      // Add role-specific fields
      if (data.role === "student") {
        const studentData = data as StudentSignupData;
        registrationData.grade_level = studentData.grade_level;
      } else if (data.role === "teacher") {
        const teacherData = data as TeacherSignupData;
        registrationData.grade_levels = teacherData.grade_levels;
        registrationData.subjects = teacherData.subjects;
        registrationData.description = teacherData.description;
        registrationData.attachments = teacherData.attachments;
      } else if (data.role === "parent") {
        const parentData = data as ParentSignupData;
        registrationData.cin_number = parentData.cin_number;
        registrationData.number_of_children = parentData.number_of_children;
        registrationData.children_grades = parentData.children_grades;
      }

      const { userId: newUserId, otpCode: receivedOtp } = await register(registrationData);

      setUserId(newUserId);
      setUserRole(data.role);
      setShowOtpForm(true);
      toast.success(t("signup.registrationSuccess"));
      // For development, show the OTP code
      toast.info(t("signup.otpCodeInfo", { code: receivedOtp }));
    } catch (error) {
      toast.error(error instanceof Error ? error.message : t("signup.registrationFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpConfirmation = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId || !otpCode) return;

    setIsConfirmingOtp(true);
    try {
      await confirmOtp(userId, otpCode);
      toast.success(t("otp.verificationSuccess"));

      // Redirect based on user role
      if (userRole === "teacher") {
        window.location.href = "/teacher-verification";
      } else {
        window.location.href = "/login";
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : t("otp.verificationFailed"));
    } finally {
      setIsConfirmingOtp(false);
    }
  };

  if (showOtpForm) {
    return (
      <div className={`min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4 ${isRTL ? "rtl" : "ltr"}`} dir={isRTL ? "rtl" : "ltr"}>
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-gray-900">{t("otp.title")}</CardTitle>
            <CardDescription className="text-gray-600">{t("otp.subtitle")}</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleOtpConfirmation} className="space-y-4">
              <div>
                <Label htmlFor="otp" className={isRTL ? "text-right" : "text-left"}>
                  {t("otp.otpCode")}
                </Label>
                <Input id="otp" type="text" placeholder={t("otp.otpPlaceholder")} value={otpCode} onChange={(e) => setOtpCode(e.target.value)} disabled={isConfirmingOtp} className="text-center text-lg tracking-wider" maxLength={6} dir="ltr" />
              </div>

              <Button type="submit" className="w-full" disabled={isConfirmingOtp || otpCode.length !== 6}>
                {isConfirmingOtp ? (
                  <>
                    <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white ${isRTL ? "ml-2" : "mr-2"}`}></div>
                    {t("otp.verifying")}
                  </>
                ) : (
                  <>
                    <CheckCircle className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("otp.verifyButton")}
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Get role-specific colors
  const getRoleColors = (role: string | null) => {
    switch (role) {
      case "student":
        return "from-blue-50 to-blue-100";
      case "teacher":
        return "from-green-50 to-green-100";
      case "parent":
        return "from-purple-50 to-purple-100";
      default:
        return "from-blue-50 to-indigo-100";
    }
  };

  return (
    <div className={`min-h-screen flex items-center justify-center bg-gradient-to-br ${getRoleColors(selectedRole)} p-4 ${isRTL ? "rtl" : "ltr"}`} dir={isRTL ? "rtl" : "ltr"}>
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900">{t("signup.title")}</CardTitle>
          <CardDescription className="text-gray-600">{t("signup.subtitle")}</CardDescription>
        </CardHeader>
        <CardContent>
          {!selectedRole ? (
            // Role Selection Form
            <Form {...roleForm}>
              <form onSubmit={roleForm.handleSubmit(handleRoleSelection)} className="space-y-4">
                <FormField
                  control={roleForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.role")}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className={isRTL ? "text-right" : "text-left"}>
                            <SelectValue placeholder={t("signup.rolePlaceholder")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="student">{t("signup.roles.student")}</SelectItem>
                          <SelectItem value="teacher">{t("signup.roles.teacher")}</SelectItem>
                          <SelectItem value="parent">{t("signup.roles.parent")}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" className="w-full">
                  {t("signup.continue")}
                </Button>
              </form>
            </Form>
          ) : (
            // Role-specific forms
            <div>
              <div className="flex items-center mb-4">
                <Button variant="ghost" size="sm" onClick={() => setSelectedRole(null)} className={`${isRTL ? "ml-2" : "mr-2"}`}>
                  <ArrowLeft className={`w-4 h-4 ${isRTL ? "ml-1" : "mr-1"}`} />
                  {t("signup.back")}
                </Button>
              </div>

              {selectedRole === "student" && <StudentSignupForm onSubmit={handleSignupSubmit} isLoading={isLoading} />}
              {selectedRole === "teacher" && <TeacherSignupForm onSubmit={handleSignupSubmit} isLoading={isLoading} />}
              {selectedRole === "parent" && <ParentSignupForm onSubmit={handleSignupSubmit} isLoading={isLoading} />}
            </div>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              {t("signup.haveAccount")}{" "}
              <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                {t("signup.signInLink")}
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Signup;
