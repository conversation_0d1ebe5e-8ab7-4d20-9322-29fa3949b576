import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff, GraduationCap, Upload, X } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { createTeacherSignupSchema, GRADES, SUBJECTS, type TeacherSignupData } from "@/schemas/signupSchemas";
import { toast } from "sonner";

interface TeacherSignupFormProps {
  onSubmit: (data: TeacherSignupData) => Promise<void>;
  isLoading: boolean;
}

const TeacherSignupForm: React.FC<TeacherSignupFormProps> = ({ onSubmit, isLoading }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [selectedGrades, setSelectedGrades] = useState<string[]>([]);
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  const [attachments, setAttachments] = useState<File[]>([]);
  const { isRTL, t } = useLanguage();

  const signupSchema = createTeacherSignupSchema(t);

  const form = useForm<TeacherSignupData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
      role: "teacher",
      language: "en",
      grade_levels: [],
      subjects: [],
      description: "",
      attachments: [],
    },
  });

  const handleSubmit = async (data: TeacherSignupData) => {
    try {
      await onSubmit(data);
    } catch (error: any) {
      // Handle field-specific errors
      if (error.field) {
        const fieldMap: Record<string, keyof TeacherSignupData> = {
          email: "email",
          phone: "phone",
        };

        const formField = fieldMap[error.field];
        if (formField) {
          form.setError(formField, {
            type: "manual",
            message: error.message,
          });
          return;
        }
      }

      // Show general error toast for non-field-specific errors
      toast.error(error.message || t("signup.registrationFailed"));
    }
  };

  const handleGradeChange = (grade: string, checked: boolean) => {
    const newGrades = checked ? [...selectedGrades, grade] : selectedGrades.filter((g) => g !== grade);
    setSelectedGrades(newGrades);
    form.setValue("grade_levels", newGrades);
  };

  const handleSubjectChange = (subject: string, checked: boolean) => {
    const newSubjects = checked ? [...selectedSubjects, subject] : selectedSubjects.filter((s) => s !== subject);
    setSelectedSubjects(newSubjects);
    form.setValue("subjects", newSubjects);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const newAttachments = [...attachments, ...files];
    setAttachments(newAttachments);
    form.setValue("attachments", newAttachments);
  };

  const removeAttachment = (index: number) => {
    const newAttachments = attachments.filter((_, i) => i !== index);
    setAttachments(newAttachments);
    form.setValue("attachments", newAttachments);
  };

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <GraduationCap className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-green-600">{t("signup.roles.teacher")}</h3>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.fullName")}</FormLabel>
                <FormControl>
                  <Input placeholder={t("signup.fullNamePlaceholder")} {...field} disabled={isLoading} className={isRTL ? "text-right" : "text-left"} dir={isRTL ? "rtl" : "ltr"} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.email")}</FormLabel>
                <FormControl>
                  <Input type="email" placeholder={t("signup.emailPlaceholder")} {...field} disabled={isLoading} className={isRTL ? "text-right" : "text-left"} dir={isRTL ? "rtl" : "ltr"} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.phone")}</FormLabel>
                <FormControl>
                  <Input type="tel" placeholder={t("signup.phonePlaceholder")} {...field} disabled={isLoading} className={isRTL ? "text-right" : "text-left"} dir={isRTL ? "rtl" : "ltr"} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="grade_levels"
            render={() => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.gradeLevels")}</FormLabel>
                <div className="grid grid-cols-3 gap-2">
                  {GRADES.map((grade) => (
                    <div key={grade} className="flex items-center space-x-2">
                      <Checkbox id={`grade-${grade}`} checked={selectedGrades.includes(grade)} onCheckedChange={(checked) => handleGradeChange(grade, checked as boolean)} />
                      <label htmlFor={`grade-${grade}`} className="text-sm">
                        {t(`signup.grades.${grade}`)}
                      </label>
                    </div>
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="subjects"
            render={() => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.subjects")}</FormLabel>
                <div className="grid grid-cols-2 gap-2">
                  {SUBJECTS.map((subject) => (
                    <div key={subject} className="flex items-center space-x-2">
                      <Checkbox id={`subject-${subject}`} checked={selectedSubjects.includes(subject)} onCheckedChange={(checked) => handleSubjectChange(subject, checked as boolean)} />
                      <label htmlFor={`subject-${subject}`} className="text-sm">
                        {t(`signup.subjects.${subject}`)}
                      </label>
                    </div>
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.description")}</FormLabel>
                <FormControl>
                  <Textarea placeholder={t("signup.descriptionPlaceholder")} {...field} disabled={isLoading} className={`min-h-[100px] ${isRTL ? "text-right" : "text-left"}`} dir={isRTL ? "rtl" : "ltr"} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="attachments"
            render={() => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.attachments")}</FormLabel>
                <FormControl>
                  <div className="space-y-2">
                    <div className="flex items-center justify-center w-full">
                      <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <Upload className="w-8 h-8 mb-4 text-gray-500" />
                          <p className="mb-2 text-sm text-gray-500">
                            <span className="font-semibold">Click to upload</span> or drag and drop
                          </p>
                          <p className="text-xs text-gray-500">PDF, DOC, DOCX (MAX. 10MB)</p>
                        </div>
                        <input type="file" className="hidden" multiple accept=".pdf,.doc,.docx" onChange={handleFileChange} />
                      </label>
                    </div>
                    {attachments.length > 0 && (
                      <div className="space-y-2">
                        {attachments.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-100 rounded">
                            <span className="text-sm truncate">{file.name}</span>
                            <Button type="button" variant="ghost" size="sm" onClick={() => removeAttachment(index)}>
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="language"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.preferredLanguage")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className={isRTL ? "text-right" : "text-left"}>
                      <SelectValue placeholder={t("signup.languagePlaceholder")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="en">{t("signup.languages.en")}</SelectItem>
                    <SelectItem value="ar">{t("signup.languages.ar")}</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.password")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input type={showPassword ? "text" : "password"} placeholder={t("signup.passwordPlaceholder")} {...field} disabled={isLoading} className={`${isRTL ? "text-right pl-12 pr-4" : "text-left pr-12 pl-4"}`} dir={isRTL ? "rtl" : "ltr"} />
                    <button type="button" onClick={() => setShowPassword(!showPassword)} className={`absolute ${isRTL ? "left-3" : "right-3"} top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700`}>
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.confirmPassword")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input type={showConfirmPassword ? "text" : "password"} placeholder={t("signup.confirmPasswordPlaceholder")} {...field} disabled={isLoading} className={`${isRTL ? "text-right pl-12 pr-4" : "text-left pr-12 pl-4"}`} dir={isRTL ? "rtl" : "ltr"} />
                    <button type="button" onClick={() => setShowConfirmPassword(!showConfirmPassword)} className={`absolute ${isRTL ? "left-3" : "right-3"} top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700`}>
                      {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full bg-green-600 hover:bg-green-700" disabled={isLoading}>
            {isLoading ? (
              <>
                <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white ${isRTL ? "ml-2" : "mr-2"}`}></div>
                {t("signup.creatingAccount")}
              </>
            ) : (
              <>
                <GraduationCap className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                {t("signup.createAccountButton")}
              </>
            )}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default TeacherSignupForm;
