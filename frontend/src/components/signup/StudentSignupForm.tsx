import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff, UserPlus } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createStudentSignupSchema, GRADES, type StudentSignupData } from "@/schemas/signupSchemas";
import { toast } from "sonner";

interface StudentSignupFormProps {
  onSubmit: (data: StudentSignupData) => Promise<void>;
  isLoading: boolean;
}

const StudentSignupForm: React.FC<StudentSignupFormProps> = ({ onSubmit, isLoading }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { isRTL, t } = useLanguage();

  const signupSchema = createStudentSignupSchema(t);

  const form = useForm<StudentSignupData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
      role: "student",
      language: "en",
      grade_level: undefined,
    },
  });

  const handleSubmit = async (data: StudentSignupData) => {
    try {
      await onSubmit(data);
    } catch (error: any) {
      // Handle field-specific errors
      if (error.field) {
        const fieldMap: Record<string, keyof StudentSignupData> = {
          email: "email",
          phone: "phone",
        };

        const formField = fieldMap[error.field];
        if (formField) {
          form.setError(formField, {
            type: "manual",
            message: error.message,
          });
          return;
        }
      }

      // Show general error toast for non-field-specific errors
      toast.error(error.message || t("signup.registrationFailed"));
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <UserPlus className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-blue-600">{t("signup.roles.student")}</h3>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.fullName")}</FormLabel>
                <FormControl>
                  <Input placeholder={t("signup.fullNamePlaceholder")} {...field} disabled={isLoading} className={isRTL ? "text-right" : "text-left"} dir={isRTL ? "rtl" : "ltr"} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.email")}</FormLabel>
                <FormControl>
                  <Input type="email" placeholder={t("signup.emailPlaceholder")} {...field} disabled={isLoading} className={isRTL ? "text-right" : "text-left"} dir={isRTL ? "rtl" : "ltr"} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.phone")}</FormLabel>
                <FormControl>
                  <Input type="tel" placeholder={t("signup.phonePlaceholder")} {...field} disabled={isLoading} className={isRTL ? "text-right" : "text-left"} dir={isRTL ? "rtl" : "ltr"} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="grade_level"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.gradeLevel")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className={isRTL ? "text-right" : "text-left"}>
                      <SelectValue placeholder={t("signup.gradeLevelPlaceholder")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {GRADES.map((grade) => (
                      <SelectItem key={grade} value={grade}>
                        {t(`signup.grades.${grade}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="language"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.preferredLanguage")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className={isRTL ? "text-right" : "text-left"}>
                      <SelectValue placeholder={t("signup.languagePlaceholder")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="en">{t("signup.languages.en")}</SelectItem>
                    <SelectItem value="ar">{t("signup.languages.ar")}</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.password")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input type={showPassword ? "text" : "password"} placeholder={t("signup.passwordPlaceholder")} {...field} disabled={isLoading} className={`${isRTL ? "text-right pl-12 pr-4" : "text-left pr-12 pl-4"}`} dir={isRTL ? "rtl" : "ltr"} />
                    <button type="button" onClick={() => setShowPassword(!showPassword)} className={`absolute ${isRTL ? "left-3" : "right-3"} top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700`}>
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={isRTL ? "text-right" : "text-left"}>{t("signup.confirmPassword")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input type={showConfirmPassword ? "text" : "password"} placeholder={t("signup.confirmPasswordPlaceholder")} {...field} disabled={isLoading} className={`${isRTL ? "text-right pl-12 pr-4" : "text-left pr-12 pl-4"}`} dir={isRTL ? "rtl" : "ltr"} />
                    <button type="button" onClick={() => setShowConfirmPassword(!showConfirmPassword)} className={`absolute ${isRTL ? "left-3" : "right-3"} top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700`}>
                      {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700" disabled={isLoading}>
            {isLoading ? (
              <>
                <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white ${isRTL ? "ml-2" : "mr-2"}`}></div>
                {t("signup.creatingAccount")}
              </>
            ) : (
              <>
                <UserPlus className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                {t("signup.createAccountButton")}
              </>
            )}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default StudentSignupForm;
